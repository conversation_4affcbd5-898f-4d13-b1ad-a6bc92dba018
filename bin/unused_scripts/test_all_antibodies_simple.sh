#!/bin/bash

# Script to test all antibodies with table format output
# Usage: bash bin/test_all_antibodies_simple.sh [model_name|all]

MODE=${1:-"igbert"}

echo "Testing All Antibodies with Table Format"
echo "========================================"
echo "Mode: $MODE"
echo "Timestamp: $(date)"
echo ""

# List of antibodies
ANTIBODIES=("mab114" "medi8852" "s309" "regn10987" "c143" "medi_uca" "mab114_uca")

# Create logs directory
mkdir -p logs/table

total_tests=0
passed_tests=0

for antibody in "${ANTIBODIES[@]}"; do
    echo "Testing $antibody..."
    echo "$(printf '=%.0s' {1..40})"

    if [ "$MODE" = "all" ]; then
        log_file="logs/table/${antibody}_all_models.log"
        if python3 bin/${antibody}.py --all-models > "$log_file" 2>&1; then
            echo "  ✓ $antibody ALL MODELS PASSED"
            ((passed_tests++))
        else
            echo "  ✗ $antibody ALL MODELS FAILED (check $log_file)"
        fi
    else
        log_file="logs/table/${antibody}_${MODE}.log"
        if python3 bin/${antibody}.py --model-name $MODE > "$log_file" 2>&1; then
            echo "  ✓ $antibody $MODE PASSED"
            ((passed_tests++))
        else
            echo "  ✗ $antibody $MODE FAILED (check $log_file)"
        fi
    fi

    ((total_tests++))
    echo ""
done

echo "SUMMARY"
echo "======="
echo "Total tests: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $((total_tests - passed_tests))"
echo "Success rate: $(echo "scale=1; $passed_tests * 100 / $total_tests" | bc)%"
echo ""
echo "Detailed logs available in: logs/table/"
echo ""
echo "Usage examples:"
echo "  bash bin/test_all_antibodies_simple.sh igbert    # Test all antibodies with IgBert"
echo "  bash bin/test_all_antibodies_simple.sh all       # Test all antibodies with all models"
