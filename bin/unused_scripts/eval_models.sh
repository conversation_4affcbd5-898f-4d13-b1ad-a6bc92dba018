#!/bin/bash

# Script to evaluate antibody models with table format output
# Usage: bash bin/eval_models.sh [antibody_name]

if [ $# -eq 0 ]; then
    echo "Usage: bash bin/eval_models.sh [antibody_name]"
    echo "Available antibodies: medi8852, medi_uca, mab114, mab114_uca, s309, regn10987, c143"
    echo ""
    echo "Examples:"
    echo "  bash bin/eval_models.sh mab114        # Test all models for mab114"
    echo "  python3 bin/mab114.py --all-models   # Same as above, direct call"
    echo "  python3 bin/mab114.py --model-name igbert  # Test single model"
    exit 1
fi

# Extract antibody name without .py extension if provided
antibody_name=$(basename "$1" .py)

echo "Evaluating $antibody_name with table format output"
echo "=================================================="

# Create logs directory if it doesn't exist
mkdir -p logs

echo "Running comprehensive analysis for $antibody_name..."
log_file="logs/${antibody_name}_all_models.log"

if python3 bin/${antibody_name}.py --all-models > "$log_file" 2>&1; then
    echo "✓ Analysis completed successfully"
    echo ""
    echo "Results summary:"
    echo "================"

    # Extract and display the summary table
    grep -A 20 "MODEL PERFORMANCE SUMMARY" "$log_file" | head -25

    echo ""
    echo "Full detailed results saved to: $log_file"
else
    echo "✗ Analysis failed (check $log_file)"
    exit 1
fi
