import os
from amis import reconstruct_multi_models

def parse_args():
    import argparse
    parser = argparse.ArgumentParser(
        description='Recommend substitutions to a wildtype sequence'
    )
    parser.add_argument('sequence', type=str,
                        help='Wildtype sequence')
    parser.add_argument(
        '--model-names',
        type=str,
        default=[ 'esm1b', 'esm1v1', 'esm1v2', 'esm1v3', 'esm1v4', 'esm1v5', 'esm2-8M', 'esm2-35M', 'esm2-150M', 'esm2-650M', 'igbert', ],
        nargs='+',
        help='Type of language model (e.g., esm1b, esm1v1, esm2-650M, igbert, igt5)'
    )
    parser.add_argument(
        '--alpha',
        type=float,
        default=None,
        help='alpha stringency parameter'
    )
    parser.add_argument(
        '--cuda',
        type=str,
        default='cuda',
        help='cuda device to use'
    )
    args = parser.parse_args()
    return args

if __name__ == '__main__':
    args = parse_args()

    if ":" in args.cuda:
        os.environ["CUDA_VISIBLE_DEVICES"] = args.cuda.split(':')[-1]

    mutations_data = reconstruct_multi_models(
        args.sequence,
        args.model_names,
        alpha=args.alpha,
        return_names=True,
    )
    
    # Print header
    print("Mutation\tCount\tOriginal Score\tMutated Score\tModels")
    
    for mutation, data in sorted(mutations_data.items(), key=lambda item: -len(item[1]['models'])):
        pos, wt, mt = mutation
        mut_str = f'{wt}{pos + 1}{mt}'
        models = ', '.join(data['models'])
        orig_score = f"{data['original_score']:.3f}" if 'original_score' in data else "N/A"
        mut_score = f"{data['mutated_score']:.3f}" if 'mutated_score' in data else "N/A"
        print(f'{mut_str}\t{len(data["models"])}\t{orig_score}\t{mut_score}\t{models}')
