#!/usr/bin/env python3
"""
Template for antibody analysis with table output format.
"""

from utils import *
from amis import get_model, encode, decode, reconstruct, compare
import time
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='C143 analysis with table output')
    parser.add_argument('--namespace', type=str, default='C143',
                        help='Model namespace')
    parser.add_argument('--model-name', type=str, default='igbert',
                        help='Type of language model (e.g., igbert, igt5, esm1b)')
    parser.add_argument('--all-models', action='store_true',
                        help='Test all available models')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    return parser.parse_args()

def analyze_mutations(original, reconstructed, chain_type):
    """Analyze mutations and return a list of mutation data."""

    mutations = []
    min_len = min(len(original), len(reconstructed))

    for i in range(min_len):
        if original[i] != reconstructed[i]:
            mutations.append({
                'position': i + 1,
                'original': original[i],
                'mutated': reconstructed[i],
                'mutation': f"{original[i]}{i+1}{reconstructed[i]}"
            })

    return mutations

def test_model(model_name, heavy_chain, light_chain, verbose=False):
    """Test a single model and return results."""

    try:
        # Create args object for get_model
        class Args:
            def __init__(self, model_name):
                self.model_name = model_name
                self.namespace = 'C143'

        args = Args(model_name)
        model = get_model(args)

        start_time = time.time()

        if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5']:
            # Paired sequence models
            result = encode(heavy_chain=heavy_chain, light_chain=light_chain, model=model)
            reconstructed = decode(result, model, exclude='unnatural')

            heavy_mutations = analyze_mutations(
                heavy_chain,
                reconstructed.get('heavy', ''),
                'heavy'
            )

            light_mutations = analyze_mutations(
                light_chain,
                reconstructed.get('light', ''),
                'light'
            )

        else:
            # Traditional single-chain models
            heavy_reconstructed = reconstruct(heavy_chain=heavy_chain, model=model)
            light_reconstructed = reconstruct(light_chain=light_chain, model=model)

            heavy_mutations = analyze_mutations(
                heavy_chain, heavy_reconstructed, 'heavy'
            )
            light_mutations = analyze_mutations(
                light_chain, light_reconstructed, 'light'
            )

        elapsed = time.time() - start_time

        return {
            'model': model_name,
            'success': True,
            'time': elapsed,
            'heavy_mutations': heavy_mutations,
            'light_mutations': light_mutations,
            'model_type': 'paired' if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5'] else 'traditional'
        }

    except Exception as e:
        return {
            'model': model_name,
            'success': False,
            'error': str(e),
            'time': 0,
            'heavy_mutations': [],
            'light_mutations': []
        }

def print_results_table(results):
    """Print results in a formatted table."""

    print("\n" + "=" * 100)
    print(f"C143 ANALYSIS RESULTS")
    print("=" * 100)

    # Summary table
    print("\nMODEL PERFORMANCE SUMMARY")
    print("-" * 80)
    print(f"{'Model':<15} {'Status':<10} {'Time (s)':<10} {'Heavy Mut':<12} {'Light Mut':<12} {'Type':<12}")
    print("-" * 80)

    for result in results:
        status = "✓ PASS" if result['success'] else "✗ FAIL"
        heavy_count = len(result['heavy_mutations'])
        light_count = len(result['light_mutations'])
        model_type = result.get('model_type', 'unknown')

        print(f"{result['model']:<15} {status:<10} {result['time']:<10.2f} {heavy_count:<12} {light_count:<12} {model_type:<12}")

    print("-" * 80)

    # Detailed mutation tables
    for result in results:
        if not result['success']:
            continue

        print(f"\n{result['model'].upper()} - DETAILED MUTATIONS")
        print("-" * 60)

        # Heavy chain mutations
        if result['heavy_mutations']:
            print("\nHeavy Chain Mutations:")
            print(f"{'Position':<10} {'Original':<10} {'Mutated':<10} {'Mutation':<15}")
            print("-" * 50)
            for mut in result['heavy_mutations']:
                print(f"{mut['position']:<10} {mut['original']:<10} {mut['mutated']:<10} {mut['mutation']:<15}")
        else:
            print("\nHeavy Chain: No mutations detected")

        # Light chain mutations
        if result['light_mutations']:
            print("\nLight Chain Mutations:")
            print(f"{'Position':<10} {'Original':<10} {'Mutated':<10} {'Mutation':<15}")
            print("-" * 50)
            for mut in result['light_mutations']:
                print(f"{mut['position']:<10} {mut['original']:<10} {mut['mutated']:<10} {mut['mutation']:<15}")
        else:
            print("\nLight Chain: No mutations detected")

        print()

if __name__ == '__main__':
    args = parse_args()

    print(f"C143 Analysis")
    print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # C143 sequences
    heavy_chain = "EVQLVESGGGLVQPGGSLRLSCAASGFSVSTKYMTWVRQAPGKGLEWVSVLYSGGSDYYADSVKGRFTISRDNSKNALYLQMNSLRVEDTGVYYCARDSSEVRDHPGHPGRSVGAFDIWGQGTMVTVSS"
    light_chain = "QSALTQPASVSGSPGQSITISCTGTSNDVGSYTLVSWYQQYPGKAPKLLIFEGTKRSSGISNRFSGSKSGNTASLTISGLQGEDEADYYCCSYAGASTFVFGGGTKLTVL"

    print(f"Heavy chain length: {len(heavy_chain)}")
    print(f"Light chain length: {len(light_chain)}")

    # Determine which models to test
    if args.all_models:
        models_to_test = ['igbert', 'igt5', 'esm1b', 'esm1v1', 'esm2-150M']
    else:
        models_to_test = [args.model_name]

    print(f"Testing models: {', '.join(models_to_test)}")

    # Test all models
    results = []
    for model_name in models_to_test:
        print(f"\nTesting {model_name}...")
        result = test_model(model_name, heavy_chain, light_chain, args.verbose)
        results.append(result)

        if result['success']:
            print(f"  ✓ {model_name} completed in {result['time']:.2f}s")
        else:
            print(f"  ✗ {model_name} failed: {result.get('error', 'Unknown error')}")

    # Print results table
    print_results_table(results)

    # Summary
    successful = sum(1 for r in results if r['success'])
    total = len(results)

    print(f"\nSUMMARY: {successful}/{total} models completed successfully")

    if successful < total:
        exit(1)
