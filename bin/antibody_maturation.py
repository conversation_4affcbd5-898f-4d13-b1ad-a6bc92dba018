#!/usr/bin/env python3
"""
Antibody maturation script that suggests mutations for user-provided antibody sequences.
Uses various protein language models to suggest potential beneficial mutations.
"""

from utils import *
from amis import get_model, encode, decode, reconstruct, compare
import time
import argparse
import pandas as pd
from collections import defaultdict
import os
from datetime import datetime
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
from Bio.Seq import Seq
from Bio import SeqIO
from Bio.SeqRecord import SeqR<PERSON>ord
from Bio.Seq import Seq

def parse_args():
    parser = argparse.ArgumentParser(description='Antibody maturation: suggest mutations for antibody sequences')
    parser.add_argument('--namespace', type=str, default='AntibodyMaturation',
                        help='Model namespace')
    parser.add_argument('--model-name', type=str, default=None,
                        help='Type of language model (e.g., igbert, esm1b, esm1v1, esm2-150M)')
    parser.add_argument('--all-models', action='store_true', default=True,
                        help='Use all available models for suggestions (default: True)')
    parser.add_argument('--single-model', action='store_true',
                        help='Use only the specified model instead of all models')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    parser.add_argument('--top-mutations', type=int, default=100,
                        help='Number of top mutations to suggest')
    parser.add_argument('--heavy', type=str, default=None,
                        help='Heavy chain sequence (provide at runtime or will be prompted)')
    parser.add_argument('--light', type=str, default=None,
                        help='Light chain sequence (provide at runtime or will be prompted)')
    parser.add_argument('--name', type=str, default=None,
                        help='Run name for output file (used in outputs/date_time_[name].tsv)')
    
    # Add batch mode options
    parser.add_argument('--input-fasta', type=str, default=None,
                        help='Input FASTA file with antibody sequences (batch mode)')
    parser.add_argument('--output-fasta', type=str, default=None,
                        help='Output FASTA file with suggested mutations (batch mode)')
    parser.add_argument('--strategy', type=str, default='plm3',
                        choices=['plm1', 'plm2', 'plm3'],
                        help='Strategy for mutation suggestions (plm1: Weighted vote ≥ 0.7, '
                             'plm2: IgBERT-CDR + ESM-1b-FR, plm3: Weighted vote ≥ 1.3)')
    parser.add_argument('--suppress-regions', action='store_true',
                        help='Suppress detailed region output in tables')
    
    return parser.parse_args()

def identify_antibody_regions(chain_type, sequence, position):
    """
    Identify antibody region (e.g., FR1, CDR1) based on chain type, sequence, and position.
    
    This function implements a simplified version of the Chothia numbering scheme,
    which is more robust to length variations in the CDR loops. It locates conserved
    residues as anchor points and positions CDRs relative to these anchors.
    
    Args:
        chain_type: 'heavy' or 'light'
        sequence: The full amino acid sequence
        position: The 1-based position in the sequence
    
    Returns:
        Region identifier string (e.g., 'CDR1-H', 'FR2-L')
    """
    # Convert to 0-based indexing for internal calculations
    pos_idx = position - 1
    
    if pos_idx < 0 or pos_idx >= len(sequence):
        return 'Unknown'
    
    # Define anchor residues and search patterns
    if chain_type == 'heavy':
        # Heavy chain has relatively conserved tryptophans (W) and cysteines (C)
        # First conserved tryptophan around position 35-38 (end of FR1)
        fr1_end = -1
        for i in range(24, min(45, len(sequence))):
            if sequence[i] == 'W':
                fr1_end = i
                break
        
        if fr1_end == -1:
            # Fallback if conserved W not found
            fr1_end = 25
        
        # Look for conserved cysteine marking the start of FR2
        fr2_start = -1
        search_start = min(fr1_end + 7, len(sequence) - 1)
        search_end = min(fr1_end + 15, len(sequence) - 1)
        for i in range(search_start, search_end):
            if i < len(sequence) and sequence[i] == 'C':
                fr2_start = i
                break
                
        if fr2_start == -1:
            fr2_start = fr1_end + 10  # Approximate if C not found
        
        # Second conserved tryptophan around position 102-108 (end of FR3)
        fr3_end = -1
        for i in range(85, min(120, len(sequence))):
            if sequence[i] == 'W':
                fr3_end = i
                break
        
        if fr3_end == -1:
            fr3_end = min(100, len(sequence) - 15)
            
        # Look for conserved pattern indicating FR4 (WGQG or similar)
        fr4_start = -1
        for i in range(fr3_end + 5, min(fr3_end + 35, len(sequence) - 4)):
            if sequence[i:i+2] == 'WG' or sequence[i:i+2] == 'FG':
                fr4_start = i
                break
                
        if fr4_start == -1:
            fr4_start = min(fr3_end + 15, len(sequence) - 10)
        
        # Define regions based on calculated boundaries
        cdr1_start = fr1_end + 1
        cdr1_end = fr2_start - 1
        fr2_end = fr2_start + 15  # Approximate FR2 length
        cdr2_start = fr2_end + 1
        cdr2_end = fr3_end - 33  # Approximate CDR2 to FR3 transition
        cdr3_start = fr3_end + 1
        cdr3_end = fr4_start - 1
        
        # Determine which region the position falls into
        if pos_idx <= fr1_end:
            return 'FR1-H'
        elif pos_idx <= cdr1_end:
            return 'CDR1-H'
        elif pos_idx <= fr2_end:
            return 'FR2-H'
        elif pos_idx <= cdr2_end:
            return 'CDR2-H'
        elif pos_idx <= fr3_end:
            return 'FR3-H'
        elif pos_idx <= cdr3_end:
            return 'CDR3-H'
        else:
            return 'FR4-H'
            
    elif chain_type == 'light':
        # Light chain has relatively conserved cysteines and tryptophans
        # First conserved cysteine around position 23
        fr1_end = -1
        for i in range(18, min(30, len(sequence))):
            if sequence[i] == 'C':
                fr1_end = i
                break
        
        if fr1_end == -1:
            fr1_end = 22  # Default approximation
        
        # Look for conserved tryptophan marking end of CDR1
        cdr1_end = -1
        search_start = min(fr1_end + 10, len(sequence) - 1)
        search_end = min(fr1_end + 17, len(sequence) - 1)
        for i in range(search_start, search_end):
            if i < len(sequence) and sequence[i] == 'W':
                cdr1_end = i - 1  # W is part of FR2
                break
                
        if cdr1_end == -1:
            cdr1_end = fr1_end + 14  # Approximate if W not found
            
        # Look for conserved cysteine marking end of FR3
        fr3_end = -1
        for i in range(80, min(100, len(sequence))):
            if sequence[i] == 'C':
                fr3_end = i
                break
                
        if fr3_end == -1:
            fr3_end = min(88, len(sequence) - 15)
            
        # Look for conserved phenylalanine indicating FR4 (FG motif)
        fr4_start = -1
        for i in range(fr3_end + 5, min(fr3_end + 25, len(sequence) - 4)):
            if i < len(sequence) - 1 and sequence[i] == 'F' and sequence[i+1] == 'G':
                fr4_start = i
                break
                
        if fr4_start == -1:
            fr4_start = min(fr3_end + 15, len(sequence) - 10)
        
        # Define regions based on calculated boundaries
        cdr1_start = fr1_end + 1
        fr2_start = cdr1_end + 1
        fr2_end = fr2_start + 14  # Approximate FR2 length
        cdr2_start = fr2_end + 1
        cdr2_end = fr2_end + 7   # Approximate CDR2 length
        cdr3_start = fr3_end + 1
        cdr3_end = fr4_start - 1
        
        # Determine which region the position falls into
        if pos_idx <= fr1_end:
            return 'FR1-L'
        elif pos_idx <= cdr1_end:
            return 'CDR1-L'
        elif pos_idx <= fr2_end:
            return 'FR2-L'
        elif pos_idx <= cdr2_end:
            return 'CDR2-L'
        elif pos_idx <= fr3_end:
            return 'FR3-L'
        elif pos_idx <= cdr3_end:
            return 'CDR3-L'
        else:
            return 'FR4-L'
    else:
        return 'Unknown'

def analyze_mutations(original, reconstructed, chain_type, chain_name=None):
    """Analyze mutations and return a list of mutation data."""
    mutations = []
    min_len = min(len(original), len(reconstructed))

    # Use chain_name if provided, otherwise fall back to chain_type
    display_chain = chain_name if chain_name else chain_type

    for i in range(min_len):
        if original[i] != reconstructed[i]:
            position = i + 1
            region = identify_antibody_regions(chain_type, original, position)
            mutations.append({
                'position': position,
                'original': original[i],
                'mutated': reconstructed[i],
                'mutation': f"{original[i]}{position}{reconstructed[i]}",
                'chain': display_chain,
                'region': region
            })

    return mutations

def get_mutations(model_name, heavy_chain, light_chain, verbose=False, heavy_name=None, light_name=None):
    """Get mutation suggestions using the specified model."""
    try:
        # Create args object for get_model
        class Args:
            def __init__(self, model_name):
                self.model_name = model_name
                self.namespace = 'AntibodyMaturation'

        args = Args(model_name)
        model = get_model(args)
        
        if verbose:
            print(f"Running model: {model_name}")
            
        start_time = time.time()
        
        all_mutations = []
        
        if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5']:
            # Paired sequence models that require both heavy and light chains
            # Check if we have both chains for paired models
            if not heavy_chain or not light_chain:
                if verbose:
                    print(f"Model {model_name} requires both heavy and light chains, but only one was provided.")
                # For batch mode with single chains, skip this model
                return {
                    'model': model_name,
                    'mutations': [],
                    'time': 0,
                    'success': False,
                    'error': f"{model_name} requires both heavy and light chains, but only one was provided."
                }
            
            # Both chains are available, proceed normally
            result = encode(heavy_chain=heavy_chain, light_chain=light_chain, model=model)
            reconstructed = decode(result, model, exclude='unnatural')
            
            # Ensure reconstructed is not None
            if reconstructed is None:
                raise ValueError(f"Model {model_name} returned None instead of reconstructed sequences")
                
            # reconstructed is a dict for these models, but could be a string for others
            heavy_seq = reconstructed.get('heavy', '') if isinstance(reconstructed, dict) else ''
            light_seq = reconstructed.get('light', '') if isinstance(reconstructed, dict) else ''
            
            if heavy_seq:
                heavy_mutations = analyze_mutations(heavy_chain, heavy_seq, 'heavy', heavy_name)
                all_mutations.extend(heavy_mutations)

            if light_seq:
                light_mutations = analyze_mutations(light_chain, light_seq, 'light', light_name)
                all_mutations.extend(light_mutations)
            
        else:
            # Traditional single-chain models
            if heavy_chain:
                heavy_reconstructed = reconstruct(heavy_chain=heavy_chain, model=model)
                heavy_mutations = analyze_mutations(heavy_chain, heavy_reconstructed, 'heavy', heavy_name)
                all_mutations.extend(heavy_mutations)

            if light_chain:
                light_reconstructed = reconstruct(light_chain=light_chain, model=model)
                light_mutations = analyze_mutations(light_chain, light_reconstructed, 'light', light_name)
                all_mutations.extend(light_mutations)
        
        elapsed = time.time() - start_time
        
        if verbose:
            print(f"Model {model_name} completed in {elapsed:.2f}s")
            
        return {
            'model': model_name,
            'mutations': all_mutations,
            'time': elapsed,
            'success': True
        }
        
    except Exception as e:
        if verbose:
            print(f"Error with model {model_name}: {str(e)}")
        return {
            'model': model_name,
            'mutations': [],
            'time': 0,
            'success': False,
            'error': str(e)
        }

def consolidate_mutations(all_results, top_n=100):
    """Consolidate mutations from multiple models and rank them."""
    # Count mutation occurrences across all models
    mutation_counts = defaultdict(int)
    mutation_details = {}
    
    for result in all_results:
        if not result['success']:
            continue
            
        for mut in result['mutations']:
            mutation_key = f"{mut['chain']}_{mut['mutation']}"
            mutation_counts[mutation_key] += 1
            
            # Store mutation details if not already stored
            if mutation_key not in mutation_details:
                mutation_details[mutation_key] = {
                    'chain': mut['chain'],
                    'position': mut['position'],
                    'original': mut['original'],
                    'mutated': mut['mutated'],
                    'mutation': mut['mutation'],
                    'region': mut['region'],
                    'models': []
                }
            
            # Add model that suggested this mutation
            if result['model'] not in mutation_details[mutation_key]['models']:
                mutation_details[mutation_key]['models'].append(result['model'])
    
    # Convert to list and sort by count (most suggested mutations first)
    ranked_mutations = []
    for mutation_key, count in mutation_counts.items():
        details = mutation_details[mutation_key]
        details['model_count'] = count
        ranked_mutations.append(details)
    
    ranked_mutations.sort(key=lambda x: x['model_count'], reverse=True)
    
    # Return top N mutations
    return ranked_mutations[:top_n]

def consolidate_mutations_weighted(all_results, top_n=100, threshold=0.7):
    """Consolidate mutations using weighted voting by model precision."""
    model_precisions = get_model_precisions()
    mutation_scores = defaultdict(float)
    mutation_details = {}

    for result in all_results:
        if not result['success']:
            continue
        model = result['model']
        precision = model_precisions.get(model, 0.0)
        for mut in result['mutations']:
            mutation_key = f"{mut['chain']}_{mut['mutation']}"
            mutation_scores[mutation_key] += precision
            if mutation_key not in mutation_details:
                mutation_details[mutation_key] = {
                    'chain': mut['chain'],
                    'position': mut['position'],
                    'original': mut['original'],
                    'mutated': mut['mutated'],
                    'mutation': mut['mutation'],
                    'region': mut['region'],
                    'models': []
                }
            if model not in mutation_details[mutation_key]['models']:
                mutation_details[mutation_key]['models'].append(model)

    # Filter by threshold
    filtered = [
        {**mutation_details[k], 'weighted_vote': v, 'model_count': len(mutation_details[k]['models'])}
        for k, v in mutation_scores.items() if v >= threshold
    ]
    filtered.sort(key=lambda x: x['weighted_vote'], reverse=True)
    return filtered[:top_n]

def consolidate_mutations_union(all_results, top_n=100, models=('igbert', 'esm1b')):
    """Union of mutations from specified models (adcurare-PLM2)."""
    mutation_details = {}
    for result in all_results:
        if not result['success'] or result['model'] not in models:
            continue
        for mut in result['mutations']:
            # Only include IgBERT for CDR regions, ESM-1b for FR regions
            if result['model'] == 'igbert' and mut['region'].startswith('CDR'):
                mutation_key = f"{mut['chain']}_{mut['mutation']}"
                if mutation_key not in mutation_details:
                    mutation_details[mutation_key] = {
                        'chain': mut['chain'],
                        'position': mut['position'],
                        'original': mut['original'],
                        'mutated': mut['mutated'],
                        'mutation': mut['mutation'],
                        'region': mut['region'],
                        'models': []
                    }
                if result['model'] not in mutation_details[mutation_key]['models']:
                    mutation_details[mutation_key]['models'].append(result['model'])
            elif result['model'] == 'esm1b' and mut['region'].startswith('FR'):
                mutation_key = f"{mut['chain']}_{mut['mutation']}"
                if mutation_key not in mutation_details:
                    mutation_details[mutation_key] = {
                        'chain': mut['chain'],
                        'position': mut['position'],
                        'original': mut['original'],
                        'mutated': mut['mutated'],
                        'mutation': mut['mutation'],
                        'region': mut['region'],
                        'models': []
                    }
                if result['model'] not in mutation_details[mutation_key]['models']:
                    mutation_details[mutation_key]['models'].append(result['model'])
    # Convert to list and sort by number of models (descending)
    union_list = list(mutation_details.values())
    for mut in union_list:
        mut['model_count'] = len(mut['models'])
    union_list.sort(key=lambda x: len(x['models']), reverse=True)
    return union_list[:top_n]

def print_mutation_suggestions(mutations):
    """Print consolidated mutation suggestions in a nice format."""
    print("\n" + "=" * 110)
    print("SUGGESTED MUTATIONS FOR ANTIBODY MATURATION")
    print("=" * 110)
    
    if not mutations:
        print("\nNo mutation suggestions were found.")
        return
    
    print(f"\nRanked by agreement across different models:")
    print("-" * 110)
    print(f"{'Rank':<5} {'Chain':<8} {'Mutation':<12} {'Position':<10} {'Region':<10} {'Original':<10} {'Suggested':<10} {'# Models':<10} {'Models':<30}")
    print("-" * 110)
    
    for i, mut in enumerate(mutations):
        print(f"{i+1:<5} {mut['chain']:<8} {mut['mutation']:<12} {mut['position']:<10} {mut['region']:<10} {mut['original']:<10} {mut['mutated']:<10} {mut['model_count']:<10} {', '.join(mut['models']):<30}")
    
    print("-" * 110)
    print("\nConsider these mutations for improving antibody affinity or stability.")
    print("Higher ranked mutations were suggested by more models and may be more reliable.")
    print("CDR region mutations may impact binding specificity, while FR region mutations often affect stability.")

def validate_sequence(seq, chain_type):
    """Validate that the input is a protein sequence."""
    if not seq:
        return False
        
    valid_amino_acids = set("ACDEFGHIKLMNPQRSTVWY")
    for aa in seq:
        if aa not in valid_amino_acids:
            print(f"Error: Invalid amino acid '{aa}' in {chain_type} chain sequence")
            return False
    
    return True

def get_user_input(args):
    """Get antibody sequences from user if not provided in args."""
    heavy_chain = args.heavy
    light_chain = args.light
    
    while not validate_sequence(heavy_chain, "heavy"):
        heavy_chain = input("\nEnter heavy chain sequence (or press Enter to skip): ").strip().upper()
        if not heavy_chain:
            print("Warning: No heavy chain sequence provided")
            break
    
    while not validate_sequence(light_chain, "light"):
        light_chain = input("\nEnter light chain sequence (or press Enter to skip): ").strip().upper()
        if not light_chain:
            print("Warning: No light chain sequence provided")
            break
    
    if not heavy_chain and not light_chain:
        print("Error: At least one chain sequence must be provided")
        exit(1)
        
    return heavy_chain, light_chain

def get_model_precisions():
    """Return a dictionary mapping model names to their precision scores."""
    return {
        'igbert': 0.47,  # IgBERT
        'esm1b': 0.48,   # ESM-1b
        'esm1v1': 0.40,
        'esm1v2': 0.40,
        'esm1v3': 0.43,
        'esm1v4': 0.48,
        'esm1v5': 0.39,
        'esm2-150M': 0.64,
        'esm2-650M': 0.47,
        # Add aliases if needed
    }

def print_combined_mutation_table(plm1, plm2, plm3, suppress_regions=False):
    """Print a combined table with adcurare PLM model columns."""
    # Build a dict for fast lookup
    def key(mut):
        return f"{mut['chain']}_{mut['mutation']}"
    plm1_dict = {key(m): m for m in plm1}
    plm2_dict = {key(m): m for m in plm2}
    plm3_dict = {key(m): m for m in plm3}
    # Union of all keys
    all_keys = set(plm1_dict) | set(plm2_dict) | set(plm3_dict)
    # For consistent order, sort by chain, position, mutation
    def sort_key(k):
        m = plm1_dict.get(k) or plm2_dict.get(k) or plm3_dict.get(k)
        if m is None:
            # fallback: sort by key string
            return (str(k),)
        return (m.get('chain',''), m.get('position',0), m.get('mutation',''))
    sorted_keys = sorted(all_keys, key=sort_key)
    print("\n" + "=" * 150)
    print("COMBINED SUGGESTED MUTATIONS FOR ANTIBODY MATURATION (adcurare PLM Models)")
    print("=" * 150)

    if suppress_regions:
        print(f"{'Rank':<5} {'Sequence':<15} {'Mutation':<12} {'Position':<10} {'Original':<10} {'Suggested':<10} {'# Models':<10} {'Models':<30} {'adcurarePLM1':<15} {'adcurarePLM2':<15} {'adcurarePLM3':<15}")
        print("-" * 150)
        for i, k in enumerate(sorted_keys):
            # Use the first available mutation for shared columns
            mut = plm1_dict.get(k) or plm2_dict.get(k) or plm3_dict.get(k)
            if mut is None:
                continue
            def present(d):
                return '✓' if k in d else ''
            print(f"{i+1:<5} {mut['chain']:<15} {mut['mutation']:<12} {mut['position']:<10} {mut['original']:<10} {mut['mutated']:<10} {mut.get('model_count',''):<10} {', '.join(mut['models']):<30} {present(plm1_dict):<15} {present(plm2_dict):<15} {present(plm3_dict):<15}")
    else:
        print(f"{'Rank':<5} {'Sequence':<15} {'Mutation':<12} {'Position':<10} {'Region':<10} {'Original':<10} {'Suggested':<10} {'# Models':<10} {'Models':<30} {'adcurarePLM1':<15} {'adcurarePLM2':<15} {'adcurarePLM3':<15}")
        print("-" * 150)
        for i, k in enumerate(sorted_keys):
            # Use the first available mutation for shared columns
            mut = plm1_dict.get(k) or plm2_dict.get(k) or plm3_dict.get(k)
            if mut is None:
                continue
            def present(d):
                return '✓' if k in d else ''
            print(f"{i+1:<5} {mut['chain']:<15} {mut['mutation']:<12} {mut['position']:<10} {mut['region']:<10} {mut['original']:<10} {mut['mutated']:<10} {mut.get('model_count',''):<10} {', '.join(mut['models']):<30} {present(plm1_dict):<15} {present(plm2_dict):<15} {present(plm3_dict):<15}")

    print("-" * 150)
    print("\n✓ in adcurarePLM1/2/3 columns means the mutation is suggested by that strategy.")
    if not suppress_regions:
        print("CDR region mutations may impact binding specificity, while FR region mutations often affect stability.")

def write_combined_mutation_table_tsv(plm1, plm2, plm3, out_path, suppress_regions=False):
    """Write the combined mutation table to a TSV file."""
    def key(mut):
        return f"{mut['chain']}_{mut['mutation']}"
    plm1_dict = {key(m): m for m in plm1}
    plm2_dict = {key(m): m for m in plm2}
    plm3_dict = {key(m): m for m in plm3}
    all_keys = set(plm1_dict) | set(plm2_dict) | set(plm3_dict)
    def sort_key(k):
        m = plm1_dict.get(k) or plm2_dict.get(k) or plm3_dict.get(k)
        if m is None:
            return (str(k),)
        return (m.get('chain',''), m.get('position',0), m.get('mutation',''))
    sorted_keys = sorted(all_keys, key=sort_key)
    with open(out_path, 'w') as f:
        if suppress_regions:
            header = ['Rank','Sequence','Mutation','Position','Original','Suggested','# Models','Models','adcurarePLM1','adcurarePLM2','adcurarePLM3']
        else:
            header = ['Rank','Sequence','Mutation','Position','Region','Original','Suggested','# Models','Models','adcurarePLM1','adcurarePLM2','adcurarePLM3']
        f.write('\t'.join(header)+'\n')
        for i, k in enumerate(sorted_keys):
            mut = plm1_dict.get(k) or plm2_dict.get(k) or plm3_dict.get(k)
            if mut is None:
                continue  # skip if no mutation info
            def present(d):
                return '✓' if k in d else ''
            if suppress_regions:
                row = [
                    str(i+1),
                    mut.get('chain',''),
                    mut.get('mutation',''),
                    str(mut.get('position','')),
                    mut.get('original',''),
                    mut.get('mutated',''),
                    str(mut.get('model_count','')),
                    ', '.join(mut.get('models',[])),
                    present(plm1_dict),
                    present(plm2_dict),
                    present(plm3_dict)
                ]
            else:
                row = [
                    str(i+1),
                    mut.get('chain',''),
                mut.get('mutation',''),
                str(mut.get('position','')),
                mut.get('region',''),
                mut.get('original',''),
                mut.get('mutated',''),
                str(mut.get('model_count','')),
                ','.join(mut.get('models',[])),
                present(plm1_dict),
                present(plm2_dict),
                present(plm3_dict)
            ]
            f.write('\t'.join(row)+'\n')

def parse_fasta_file(fasta_path):
    """Parse a FASTA file and return a dictionary of sequences."""
    sequences = {}
    
    try:
        for record in SeqIO.parse(fasta_path, "fasta"):
            seq_id = record.id
            sequence = str(record.seq).upper()
            sequences[seq_id] = sequence
        return sequences
    except Exception as e:
        print(f"Error parsing FASTA file: {str(e)}")
        return {}

def determine_chain_type(seq_id):
    """Determine chain type (heavy or light) based on sequence ID."""
    seq_id_lower = seq_id.lower()

    # Check for light chain indicators first (more specific patterns)
    if seq_id_lower.endswith('_l'):
        return 'light'
    if seq_id_lower.endswith('_light'):
        return 'light'
    if seq_id_lower.endswith('_vl'):
        return 'light'
    if seq_id_lower.endswith('_lambda'):
        return 'light'
    if seq_id_lower.endswith('_kappa'):
        return 'light'

    # Check for heavy chain indicators
    if seq_id_lower.endswith('_h'):
        return 'heavy'
    if seq_id_lower.endswith('_heavy'):
        return 'heavy'
    if seq_id_lower.endswith('_vh'):
        return 'heavy'

    # Check for patterns within the string (less specific)
    light_indicators = ['light', 'vl', 'lambda', 'kappa']
    for indicator in light_indicators:
        if indicator in seq_id_lower:
            return 'light'

    heavy_indicators = ['heavy', 'vh']
    for indicator in heavy_indicators:
        if indicator in seq_id_lower:
            return 'heavy'

    # Default to unknown if we can't determine
    return 'unknown'

def generate_mutant_id(seq_id, mutations):
    """Generate a new ID for a sequence with mutations.
    
    Args:
        seq_id: Original sequence ID
        mutations: List of mutation dictionaries
    
    Returns:
        New sequence ID with mutation details
    """
    if not mutations:
        return f"{seq_id}_no_mutations"
        
    # Sort mutations by position for readability
    sorted_mutations = sorted(mutations, key=lambda x: x['position'])
    
    # Generate mutation string (e.g., A34T_K84Y)
    mutation_str = '_'.join([mut['mutation'] for mut in sorted_mutations])
    
    return f"{seq_id}_{mutation_str}"

def apply_mutations(sequence, mutations):
    """Apply a list of mutations to a sequence.
    
    Args:
        sequence: Original amino acid sequence
        mutations: List of mutation dictionaries
    
    Returns:
        New sequence with mutations applied
    """
    # Convert to list for easier manipulation
    seq_list = list(sequence)
    
    # Sort mutations by position in descending order
    # This ensures earlier mutations don't affect positions of later ones
    sorted_mutations = sorted(mutations, key=lambda x: x['position'], reverse=True)
    
    for mut in sorted_mutations:
        pos = mut['position'] - 1  # Convert to 0-based indexing
        original = mut['original']
        mutated = mut['mutated']
        
        # Verify that the original amino acid matches
        if seq_list[pos] != original:
            print(f"Warning: Expected {original} at position {pos+1}, found {seq_list[pos]}")
            continue
            
        # Apply mutation
        seq_list[pos] = mutated
    
    return ''.join(seq_list)

def write_mutated_sequences_to_fasta(output_path, original_seqs, seq_to_mutations):
    """Write original and mutated sequences to a FASTA file.
    
    Args:
        output_path: Path to write the FASTA file
        original_seqs: Dictionary of original sequences
        seq_to_mutations: Dictionary mapping sequence IDs to mutations
    """
    records = []
    
    # First add all original sequences
    for seq_id, sequence in original_seqs.items():
        record = SeqRecord(
            Seq(sequence),
            id=seq_id,
            description=f"Original sequence"
        )
        records.append(record)
        
        # Add mutated sequence if we have mutations for this ID
        if seq_id in seq_to_mutations and seq_to_mutations[seq_id]:
            mutations = seq_to_mutations[seq_id]
            mutated_seq = apply_mutations(sequence, mutations)
            mutant_id = generate_mutant_id(seq_id, mutations)
            
            mutant_record = SeqRecord(
                Seq(mutated_seq),
                id=mutant_id,
                description=f"Mutated sequence with {len(mutations)} mutations"
            )
            records.append(mutant_record)
    
    # Write to FASTA file
    with open(output_path, 'w') as output_handle:
        SeqIO.write(records, output_handle, "fasta")
    
    print(f"FASTA file with original and mutated sequences written to: {output_path}")

def process_batch(fasta_path, args):
    """Process multiple sequences from a FASTA file in batch mode."""
    sequences = parse_fasta_file(fasta_path)
    
    if not sequences:
        print("No valid sequences found in the input FASTA file.")
        return 1
        
    print(f"Found {len(sequences)} sequences in the input file.")
    
    # Dictionary to store mutations for each sequence
    seq_to_mutations = {}
    
    # Group sequences by antibody base name to find paired heavy/light chains
    antibody_groups = {}
    
    for seq_id, sequence in sequences.items():
        # Extract base name by removing _heavy, _light, etc.
        base_name = seq_id.lower()
        # Remove common suffixes (case insensitive) - more comprehensive list
        suffixes_to_remove = ['_heavy', '_h', '_vh', '_light', '_l', '_vl', '_lambda', '_kappa']
        for suffix in suffixes_to_remove:
            if base_name.endswith(suffix):
                base_name = base_name[:-len(suffix)]
                break

        if base_name not in antibody_groups:
            antibody_groups[base_name] = {}

        chain_type = determine_chain_type(seq_id)

        if chain_type != 'unknown':
            antibody_groups[base_name][chain_type] = (seq_id, sequence)
    
    # Process each antibody (either paired or single chain)
    for base_name, chains in antibody_groups.items():
        has_heavy = 'heavy' in chains
        has_light = 'light' in chains
        
        # Process paired chains first if both are available
        if has_heavy and has_light:
            heavy_id, heavy_seq = chains['heavy']
            light_id, light_seq = chains['light']
            
            print(f"\nProcessing paired antibody: {base_name}")
            print(f"  Heavy chain: {heavy_id}")
            print(f"  Light chain: {light_id}")
            
            # Get mutations using all models including paired models
            results = []
            if args.all_models:
                models_to_test = ['igbert', 'esm1b', 'esm1v1', 'esm1v2', 'esm1v3', 'esm1v4', 'esm1v5', 'esm2-150M', 'esm2-650M']
            else:
                models_to_test = [args.model_name]
            
            for model_name in models_to_test:
                print(f"  Querying {model_name}...")
                result = get_mutations(model_name, heavy_seq, light_seq, args.verbose, heavy_id, light_id)
                results.append(result)
                
                if result['success']:
                    mutation_count = len(result['mutations'])
                    print(f"    ✓ {model_name} suggested {mutation_count} mutations in {result['time']:.2f}s")
                else:
                    print(f"    ✗ {model_name} failed: {result.get('error', 'Unknown error')}")
            
            # Get mutations based on strategy
            weighted_07 = consolidate_mutations_weighted(results, args.top_mutations, threshold=0.7)
            union_igbert_esm1b = consolidate_mutations_union(results, args.top_mutations, models=('igbert', 'esm1b'))
            weighted_13 = consolidate_mutations_weighted(results, args.top_mutations, threshold=1.3)
            
            # Select mutations based on strategy
            if args.strategy == 'plm1':
                selected_mutations = weighted_07
                strategy_name = "adcurare-PLM1 (Weighted vote ≥ 0.7)"
            elif args.strategy == 'plm2':
                selected_mutations = union_igbert_esm1b
                strategy_name = "adcurare-PLM2 (IgBERT-CDR ∪ ESM-1b-FR)"
            else:  # plm3
                selected_mutations = weighted_13
                strategy_name = "adcurare-PLM3 (Weighted vote ≥ 1.3)"
            
            # Separate mutations by chain ID (since we now use sequence names as chain identifiers)
            heavy_mutations = [mut for mut in selected_mutations if mut['chain'] == heavy_id]
            light_mutations = [mut for mut in selected_mutations if mut['chain'] == light_id]
            
            # Store mutations for each chain
            seq_to_mutations[heavy_id] = heavy_mutations
            seq_to_mutations[light_id] = light_mutations
            
            # Write TSV file for paired antibody
            out_dir = os.path.join(os.path.dirname(__file__), '../outputs')
            os.makedirs(out_dir, exist_ok=True)
            dt_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            out_name = f"{dt_str}_{base_name}_paired"
            out_path = os.path.join(out_dir, f"{out_name}.tsv")
            write_combined_mutation_table_tsv(weighted_07, union_igbert_esm1b, weighted_13, out_path, args.suppress_regions)
            print(f"  Mutation details written to: {out_path}")
            
            print(f"  Heavy chain: {len(heavy_mutations)} mutations, Light chain: {len(light_mutations)} mutations")
            print(f"  Selected using {strategy_name}")
        
        # Process each individual chain separately
        else:
            for chain_type, (seq_id, sequence) in chains.items():
                print(f"\nProcessing {seq_id} ({chain_type} chain)")
                
                # Set up appropriate arguments based on chain type
                if chain_type == 'heavy':
                    heavy_chain = sequence
                    light_chain = None
                else:  # light chain
                    heavy_chain = None
                    light_chain = sequence
                    
                # Get mutations (skip igbert for single chains)
                results = []
                if args.all_models:
                    models_to_test = ['esm1b', 'esm1v1', 'esm1v2', 'esm1v3', 'esm1v4', 'esm1v5', 'esm2-150M', 'esm2-650M']
                else:
                    # Skip igbert if it was selected but we only have one chain
                    if args.model_name == 'igbert':
                        print(f"  Skipping igbert as it requires paired heavy/light chains")
                        models_to_test = ['esm1b']  # Use esm1b as fallback
                    else:
                        models_to_test = [args.model_name]
                
                for model_name in models_to_test:
                    print(f"  Querying {model_name}...")
                    result = get_mutations(model_name, heavy_chain, light_chain, args.verbose, seq_id if chain_type == 'heavy' else None, seq_id if chain_type == 'light' else None)
                    results.append(result)
                    
                    if result['success']:
                        mutation_count = len(result['mutations'])
                        print(f"    ✓ {model_name} suggested {mutation_count} mutations in {result['time']:.2f}s")
                    else:
                        print(f"    ✗ {model_name} failed: {result.get('error', 'Unknown error')}")
                
                # Get mutations based on strategy
                weighted_07 = consolidate_mutations_weighted(results, args.top_mutations, threshold=0.7)
                
                # For single chains, we can't use PLM2 strategy that depends on igbert for CDRs
                # So adapt the strategy to use only the available data
                if args.strategy == 'plm2':
                    # If we only have one chain and the strategy is plm2,
                    # we'll use esm1b for all regions as a fallback
                    single_model_results = [r for r in results if r['model'] == 'esm1b' and r['success']]
                    if single_model_results:
                        union_igbert_esm1b = consolidate_mutations([single_model_results[0]], args.top_mutations)
                        print(f"  Note: Using esm1b for all regions (instead of IgBERT-CDR + ESM-1b-FR)")
                    else:
                        # If esm1b failed, just use weighted_07 as fallback
                        union_igbert_esm1b = weighted_07
                        print(f"  Note: Falling back to PLM1 strategy as esm1b failed")
                else:
                    # For PLM1/PLM3, proceed normally with weighted voting
                    union_igbert_esm1b = []
                
                weighted_13 = consolidate_mutations_weighted(results, args.top_mutations, threshold=1.3)
                
                # Select mutations based on strategy
                if args.strategy == 'plm1':
                    selected_mutations = weighted_07
                    strategy_name = "adcurare-PLM1 (Weighted vote ≥ 0.7)"
                elif args.strategy == 'plm2':
                    selected_mutations = union_igbert_esm1b
                    strategy_name = "adcurare-PLM2 (ESM-1b substituting for IgBERT-CDR + ESM-1b-FR)"
                else:  # plm3
                    selected_mutations = weighted_13
                    strategy_name = "adcurare-PLM3 (Weighted vote ≥ 1.3)"
                    
                # Store mutations for this sequence
                seq_to_mutations[seq_id] = selected_mutations
                
                # Print summary for this sequence
                print(f"  {len(selected_mutations)} mutations selected using {strategy_name}")
                
                # Write TSV file for individual sequence
                out_dir = os.path.join(os.path.dirname(__file__), '../outputs')
                os.makedirs(out_dir, exist_ok=True)
                dt_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                out_name = f"{dt_str}_{seq_id}"
                out_path = os.path.join(out_dir, f"{out_name}.tsv")
                write_combined_mutation_table_tsv(weighted_07, union_igbert_esm1b, weighted_13, out_path, args.suppress_regions)
                print(f"  Mutation details written to: {out_path}")
    
    # Write mutated sequences to FASTA file if output path provided
    if args.output_fasta:
        write_mutated_sequences_to_fasta(args.output_fasta, sequences, seq_to_mutations)
    
    print("\nAll sequences processed successfully.")
    return 0

def main():
    args = parse_args()
    
    print("\nANTIBODY MATURATION")
    print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Check if we are running in batch mode
    if args.input_fasta:
        print(f"Running in batch mode using input FASTA file: {args.input_fasta}")
        return process_batch(args.input_fasta, args)
    
    # Interactive single sequence mode
    # Get antibody sequences
    heavy_chain, light_chain = get_user_input(args)
    
    if heavy_chain:
        print(f"\nHeavy chain length: {len(heavy_chain)}")
        print(f"Heavy chain: {heavy_chain[:10]}...{heavy_chain[-10:]}")
    
    if light_chain:
        print(f"\nLight chain length: {len(light_chain)}")
        print(f"Light chain: {light_chain[:10]}...{light_chain[-10:]}")
    
    # Determine which models to test
    if args.single_model and args.model_name:
        models_to_test = [args.model_name]
    else:
        # Default to all models
        models_to_test = ['igbert', 'esm1b', 'esm1v1', 'esm1v2', 'esm1v3', 'esm1v4', 'esm1v5', 'esm2-150M', 'esm2-650M']
    
    print(f"\nUsing models: {', '.join(models_to_test)}")
    
    # Get mutation suggestions from all models
    results = []
    for model_name in models_to_test:
        print(f"Querying {model_name} for mutation suggestions...")
        result = get_mutations(model_name, heavy_chain, light_chain, args.verbose)
        results.append(result)
        
        if result['success']:
            mutation_count = len(result['mutations'])
            print(f"  ✓ {model_name} suggested {mutation_count} mutations in {result['time']:.2f}s")
        else:
            print(f"  ✗ {model_name} failed: {result.get('error', 'Unknown error')}")
    
    # Consolidate mutations from all models (original method)
    suggested_mutations = consolidate_mutations(results, args.top_mutations)

    # Strategy 1: Weighted vote ≥ 0.7 (adcurare-PLM1)
    weighted_07 = consolidate_mutations_weighted(results, args.top_mutations, threshold=0.7)
    # Strategy 2: IgBERT-CDR ∪ ESM-1b-FR (adcurare-PLM2)
    union_igbert_esm1b = consolidate_mutations_union(results, args.top_mutations, models=('igbert', 'esm1b'))
    # Strategy 3: Weighted vote ≥ 1.3 (adcurare-PLM3)
    weighted_13 = consolidate_mutations_weighted(results, args.top_mutations, threshold=1.3)

    # Print combined mutation table for all strategies
    print_combined_mutation_table(weighted_07, union_igbert_esm1b, weighted_13, args.suppress_regions)

    # Write combined mutation table to outputs/ folder
    out_dir = os.path.join(os.path.dirname(__file__), '../outputs')
    os.makedirs(out_dir, exist_ok=True)
    dt_str = datetime.now().strftime('%Y%m%d_%H%M%S')
    out_name = f"{dt_str}"
    if hasattr(args, 'name') and args.name:
        out_name += f"_{args.name}"
    out_path = os.path.join(out_dir, f"{out_name}.tsv")
    write_combined_mutation_table_tsv(weighted_07, union_igbert_esm1b, weighted_13, out_path, args.suppress_regions)
    print(f"\nCombined mutation table written to: {out_path}\n")
    
    # If output FASTA file is requested for single sequence mode
    if args.output_fasta:
        print(f"Generating mutated sequences in FASTA format...")
        
        # Determine which mutations to use based on strategy
        if args.strategy == 'plm1':
            selected_mutations = weighted_07
            strategy_name = "adcurare-PLM1 (Weighted vote ≥ 0.7)"
        elif args.strategy == 'plm2':
            selected_mutations = union_igbert_esm1b
            strategy_name = "adcurare-PLM2 (IgBERT-CDR ∪ ESM-1b-FR)"
        else:  # plm3
            selected_mutations = weighted_13
            strategy_name = "adcurare-PLM3 (Weighted vote ≥ 1.3)"
        
        # Create sequences dict
        original_seqs = {}
        seq_to_mutations = {}
        
        if heavy_chain:
            heavy_id = "heavy" if not args.name else f"{args.name}_heavy"
            original_seqs[heavy_id] = heavy_chain
            # Filter mutations for heavy chain
            heavy_mutations = [mut for mut in selected_mutations if mut['chain'] == 'heavy']
            if heavy_mutations:
                seq_to_mutations[heavy_id] = heavy_mutations
        
        if light_chain:
            light_id = "light" if not args.name else f"{args.name}_light"
            original_seqs[light_id] = light_chain
            # Filter mutations for light chain
            light_mutations = [mut for mut in selected_mutations if mut['chain'] == 'light']
            if light_mutations:
                seq_to_mutations[light_id] = light_mutations
        
        write_mutated_sequences_to_fasta(args.output_fasta, original_seqs, seq_to_mutations)
        print(f"FASTA file with original and mutated sequences written to: {args.output_fasta}")

    # Summary
    successful = sum(1 for r in results if r['success'])
    total = len(results)
    print(f"\nSUMMARY: {successful}/{total} models completed successfully")
    if successful < total:
        print("Warning: Some models failed to provide suggestions.")
    return 0

if __name__ == "__main__":
    exit(main())
