import torch
import numpy as np
import warnings
from transformers import T5EncoderModel, T5Tokenizer

class IgT5Model(object):
    def __init__(self, name='Exscientia/IgT5'):
        self.name_ = name

        with warnings.catch_warnings():
            warnings.simplefilter('ignore', UserWarning)
            self.tokenizer_ = T5Tokenizer.from_pretrained(name, do_lower_case=False)
            self.model_ = T5EncoderModel.from_pretrained(name)

        self.model_.eval()
        if torch.cuda.is_available():
            self.model_ = self.model_.cuda()

        # Create a simple alphabet-like interface for compatibility
        self.alphabet_ = SimpleAlphabet()
        self.offset_ = 1

    def predict_sequence_prob(self, heavy_chain="", light_chain=""):
        """
        Use IgT5's encoder to get embeddings for paired sequences.

        Args:
            heavy_chain: Heavy chain sequence (default: empty string)
            light_chain: Light chain sequence (default: empty string)

        Returns:
            Dictionary with 'heavy' and 'light' embeddings for both chains
        """
        # Format as paired sequence with spaces between amino acids and </s> separator
        # The tokenizer expects input of the form ["V Q ... S S </s> E V ... I K", ...]
        heavy_spaced = ' '.join(heavy_chain) if heavy_chain else ""
        light_spaced = ' '.join(light_chain) if light_chain else ""
        paired_seq = heavy_spaced + ' </s> ' + light_spaced

        # Tokenize the sequence
        tokens = self.tokenizer_.batch_encode_plus(
            [paired_seq],
            add_special_tokens=True,
            padding=True,
            return_tensors="pt",
            return_special_tokens_mask=True
        )

        if torch.cuda.is_available():
            tokens = {k: v.cuda() for k, v in tokens.items()}

        # Get embeddings from T5 encoder
        with torch.no_grad():
            output = self.model_(
                input_ids=tokens['input_ids'],
                attention_mask=tokens['attention_mask']
            )
            embeddings = output.last_hidden_state[0]  # Remove batch dimension

            # Find the </s> token to separate heavy and light chains
            eos_token_id = self.tokenizer_.eos_token_id
            input_ids = tokens['input_ids'][0]
            eos_positions = []
            for i, token_id in enumerate(input_ids):
                if token_id == eos_token_id:
                    eos_positions.append(i)
            eos_positions = torch.tensor(eos_positions)

            result = {}

            if len(eos_positions) > 0:
                eos_pos = eos_positions[0].item()
                # Heavy chain embeddings (up to first </s>)
                heavy_embeddings = embeddings[:eos_pos]
                # Light chain embeddings (after </s> token, excluding final tokens)
                light_embeddings = embeddings[eos_pos+1:-1]  # Skip </s> and final tokens
            else:
                # Fallback: split embeddings in half
                mid_point = embeddings.shape[0] // 2
                heavy_embeddings = embeddings[:mid_point]
                light_embeddings = embeddings[mid_point:-1]

            # Adjust embeddings to match sequence lengths
            if heavy_chain:
                if heavy_embeddings.shape[0] > len(heavy_chain):
                    heavy_embeddings = heavy_embeddings[:len(heavy_chain)]
                elif heavy_embeddings.shape[0] < len(heavy_chain):
                    embed_dim = heavy_embeddings.shape[1]
                    padding = torch.zeros(len(heavy_chain) - heavy_embeddings.shape[0], embed_dim)
                    if torch.cuda.is_available():
                        padding = padding.cuda()
                    heavy_embeddings = torch.cat([heavy_embeddings, padding], dim=0)
                result['heavy'] = heavy_embeddings.cpu().numpy()

            if light_chain:
                if light_embeddings.shape[0] > len(light_chain):
                    light_embeddings = light_embeddings[:len(light_chain)]
                elif light_embeddings.shape[0] < len(light_chain):
                    embed_dim = light_embeddings.shape[1]
                    padding = torch.zeros(len(light_chain) - light_embeddings.shape[0], embed_dim)
                    if torch.cuda.is_available():
                        padding = padding.cuda()
                    light_embeddings = torch.cat([light_embeddings, padding], dim=0)
                result['light'] = light_embeddings.cpu().numpy()

        return result

    def encode(self, heavy_chain="", light_chain=""):
        """
        Encode paired sequences using IgT5 embeddings

        Args:
            heavy_chain: Heavy chain sequence (default: empty string)
            light_chain: Light chain sequence (default: empty string)

        Returns:
            Dictionary with 'heavy' and 'light' embeddings for both chains
        """
        return self.predict_sequence_prob(heavy_chain=heavy_chain, light_chain=light_chain)

    def decode(self, embeddings_dict):
        """
        For IgT5, we don't have direct MLM capabilities like BERT.
        We'll use a similarity-based approach to find the most likely amino acids.

        Args:
            embeddings_dict: Dictionary with 'heavy' and/or 'light' embeddings

        Returns:
            Dictionary with 'heavy' and/or 'light' sequences
        """
        # Since T5 encoder doesn't have MLM, we'll use a simple approach
        # based on embedding similarity to canonical amino acid embeddings

        amino_acids = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I',
                      'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V']

        # Get embeddings for individual amino acids
        aa_embeddings = []
        for aa in amino_acids:
            tokens = self.tokenizer_.encode(aa, return_tensors="pt", add_special_tokens=False)
            if torch.cuda.is_available():
                tokens = tokens.cuda()

            with torch.no_grad():
                output = self.model_(input_ids=tokens)
                aa_embedding = output.last_hidden_state[0, 0]  # First token embedding
                aa_embeddings.append(aa_embedding.cpu().numpy())

        aa_embeddings = np.stack(aa_embeddings)  # Shape: (20, embed_dim)

        result = {}

        for chain_type, embeddings in embeddings_dict.items():
            # For each position, find the most similar amino acid
            predicted_sequence = []
            for i in range(embeddings.shape[0]):
                pos_embedding = embeddings[i]  # Shape: (embed_dim,)

                # Compute cosine similarity with all amino acid embeddings
                similarities = np.dot(aa_embeddings, pos_embedding) / (
                    np.linalg.norm(aa_embeddings, axis=1) * np.linalg.norm(pos_embedding)
                )

                # Get the amino acid with highest similarity
                best_aa_idx = np.argmax(similarities)
                predicted_sequence.append(amino_acids[best_aa_idx])

            result[chain_type] = ''.join(predicted_sequence)

        return result


class SimpleAlphabet(object):
    """
    Simple alphabet class for compatibility with ESM interface
    """
    def __init__(self):
        # Standard amino acid alphabet
        self.all_toks = [
            '<cls>', '<pad>', '<eos>', '<unk>',
            'L', 'A', 'G', 'V', 'S', 'E', 'R', 'T', 'I', 'D', 'P', 'K',
            'Q', 'N', 'F', 'Y', 'M', 'H', 'W', 'C', 'X', 'B', 'U', 'Z', 'O', '.', '-'
        ]
        self.tok_to_idx = {tok: i for i, tok in enumerate(self.all_toks)}
        self.padding_idx = self.tok_to_idx['<pad>']

    def get_idx(self, tok):
        return self.tok_to_idx.get(tok, self.tok_to_idx['<unk>'])

    def get_tok(self, idx):
        return self.all_toks[idx] if 0 <= idx < len(self.all_toks) else '<unk>'
