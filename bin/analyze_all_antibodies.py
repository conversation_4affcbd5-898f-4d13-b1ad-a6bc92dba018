#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run antibody maturation analysis on a collection of antibodies.
Automatically generates commands to analyze all antibodies using all available models.
"""

import os
import argparse
import subprocess
import time

# Define antibody sequences
ANTIBODIES = {
    "mab114": {
        "heavy": "EVQLVESGGGLIQPGGSLRLSCAASGFALRMYDMHWVRQTIDKRLEWVSAVGPSGDTYYADSVKGRFAVSRENAKNSLSLQMNSLTAGDTAIYYCVRSDRGVAGLFDSWGQGILVTVSS",
        "light": "DIQMTQSPSSLSASVGDRITITCRASQAFDNYVAWYQQRPGKVPKLLISAASALHAGVPSRFSGSGSGTHFTLTISSLQPEDVATYYCQNYNSAPLTFGGGTKVEIK",
        "description": "Ebola virus therapeutic antibody"
    },
    "mab114_uca": {
        "heavy": "EVQLVESGGGLVQPGGSLRLSCAASGFTFSSYDMHWVRQATGKGLEWVSAIGTAGDTYYPGSVKGRFTISRENAKNSLYLQMNSLRAGDTAVYYCVRSDRGVAGLFDSWGQGTLVTVSS",
        "light": "DIQMTQSPSSLSASVGDRVTITCRASQGISNYLAWYQQKPGKVPKLLIYAASTLQSGVPSRFSGSGSGTDFTLTISSLQPEDVATYYCQKYNSAPLTFGGGTKVEIK",
        "description": "Unmutated common ancestor of mAb114"
    },
    "s309": {
        "heavy": "QVQLVQSGAEVKKPGASVKVSCKASGYPFTSYGISWVRQAPGQGLEWMGWISTYNGNTNYAQKFQGRVTMTTDTSTTTGYMELRRLRSDDTAVYYCARDYTRGAWFGESLIGGFDNWGQGTLVTVSS",
        "light": "EIVLTQSPGTLSLSPGERATLSCRASQTVSSTSLAWYQQKPGQAPRLLIYGASSRATGIPDRFSGSGSGTDFTLTISRLEPEDFAVYYCQQHDTSLTFGGGTKVEIK",
        "description": "SARS-CoV-2 neutralizing antibody targeting the RBD"
    },
    "regn10987": {
        "heavy": "QVQLVESGGGVVQPGRSLRLSCAASGFTFSNYAMYWVRQAPGKGLEWVAVISYDGSNKYYADSVKGRFTISRDNSKNTLYLQMNSLRTEDTAVYYCASGSDYGDYLLVYWGQGTLVTVSS",
        "light": "QSALTQPASVSGSPGQSITISCTGTSSDVGGYNYVSWYQQHPGKAPKLMIYDVSKRPSGVSNRFSGSKSGNTASLTISGLQSEDEADYYCNSLTSISTWVFGGGTKLTVL",
        "description": "SARS-CoV-2 neutralizing antibody, part of REGN-COV2 cocktail"
    },
    "medi8852": {
        "heavy": "QVQLQQSGPGLVKPSQTLSLTCAISGDSVSSYNAVWNWIRQSPSRGLEWLGRTYYRSGWYNDYAESVKSRITINPDTSKNQFSLQLNSVTPEDTAVYYCARSGHITVFGVNVDAFDMWGQGTMVTVSS",
        "light": "DIQMTQSPSSLSASVGDRVTITCRTSQSLSSYTHWYQQKPGKAPKLLIYAASSRGSGVPSRFSGSGSGTDFTLTISSLQPEDFATYYCQQSRTFGQGTKVEIK",
        "description": "Broadly neutralizing influenza antibody"
    },
    "c143": {
        "heavy": "EVQLVESGGGLVQPGGSLRLSCAASGFSVSTKYMTWVRQAPGKGLEWVSVLYSGGSDYYADSVKGRFTISRDNSKNALYLQMNSLRVEDTGVYYCARDSSEVRDHPGHPGRSVGAFDIWGQGTMVTVSS",
        "light": "QSALTQPASVSGSPGQSITISCTGTSNDVGSYTLVSWYQQYPGKAPKLLIFEGTKRSSGISNRFSGSKSGNTASLTISGLQGEDEADYYCCSYAGASTFVFGGGTKLTVL",
        "description": "SARS-CoV-2 neutralizing antibody targeting the RBD"
    }
}

def parse_args():
    parser = argparse.ArgumentParser(description='Run antibody maturation analysis on all available antibodies')
    parser.add_argument('--output-dir', type=str, default='logs',
                        help='Directory where log files will be saved')
    parser.add_argument('--antibody', type=str, default=None,
                        help='Specific antibody to analyze (default: analyze all)')
    parser.add_argument('--top-mutations', type=int, default=100,
                        help='Number of top mutations to report')
    parser.add_argument('--verbose', action='store_true',
                        help='Print verbose output')
    parser.add_argument('--dry-run', action='store_true',
                        help='Show commands without executing them')
    return parser.parse_args()

def ensure_dir(directory):
    """Ensure that a directory exists."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def run_antibody_analysis(args):
    """Run analysis on specified antibodies."""
    ensure_dir(args.output_dir)
    
    # Determine which antibodies to analyze
    antibodies_to_analyze = {}
    if args.antibody:
        if args.antibody in ANTIBODIES:
            antibodies_to_analyze[args.antibody] = ANTIBODIES[args.antibody]
        else:
            print(f"Error: Antibody '{args.antibody}' not found in database")
            print(f"Available antibodies: {', '.join(ANTIBODIES.keys())}")
            return
    else:
        antibodies_to_analyze = ANTIBODIES
    
    print(f"Starting analysis at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'Antibody':<15} {'Status':<10} {'Start Time':<20} {'Duration':<10}")
    print("-" * 60)
    
    all_commands = []
    
    for antibody_name, antibody_data in antibodies_to_analyze.items():
        # Build the command
        log_file = os.path.join(args.output_dir, f"{antibody_name}_maturation.log")
        
        command = [
            "python", "bin/antibody_maturation.py",
            "--heavy", antibody_data["heavy"], 
            "--light", antibody_data["light"],
            "--all-models",
            "--top-mutations", str(args.top_mutations),
            "--name", antibody_name
        ]
        
        if args.verbose:
            command.append("--verbose")
            
        command_str = " ".join(command) + f" > {log_file}"
        all_commands.append((antibody_name, command_str, log_file))
    
    # Execute commands
    for antibody_name, command_str, log_file in all_commands:
        print(f"{antibody_name:<15} {'Starting...':<10} {time.strftime('%H:%M:%S'):<20}", end='', flush=True)
        
        start_time = time.time()
        
        if args.dry_run:
            print(f"\n  CMD: {command_str}\n")
            elapsed = 0
        else:
            # Execute the command
            try:
                exit_code = subprocess.run(command_str, shell=True).returncode
                
                elapsed = time.time() - start_time
                status = "✓ COMPLETE" if exit_code == 0 else f"✗ FAILED ({exit_code})"
                
                # Print elapsed time and status
                print(f"\r{antibody_name:<15} {status:<10} {time.strftime('%H:%M:%S', time.localtime(start_time)):<20} {elapsed:.2f}s")
                
                # Print brief summary of results
                if exit_code == 0:
                    try:
                        with open(log_file, 'r') as f:
                            content = f.read()
                            mutation_count = content.count('CDR')
                            print(f"  Found {mutation_count} CDR region mutations in suggested improvements")
                    except Exception as e:
                        if args.verbose:
                            print(f"  Error reading log file: {str(e)}")
            except Exception as e:
                print(f"\r{antibody_name:<15} {'✗ ERROR':<10} {time.strftime('%H:%M:%S', time.localtime(start_time)):<20}")
                if args.verbose:
                    print(f"  Error: {str(e)}")
    
    print("-" * 60)
    print(f"Completed at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Print a summary of all commands for reference
    print("\nCOMMAND SUMMARY:")
    for antibody_name, command_str, _ in all_commands:
        print(f"{antibody_name:<15}: {command_str}")

if __name__ == "__main__":
    args = parse_args()
    run_antibody_analysis(args)
