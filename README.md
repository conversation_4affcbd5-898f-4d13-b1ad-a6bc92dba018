# Efficient Antibody Evolution

This repository provides tools for analyzing and suggesting beneficial mutations for antibody sequences using multiple protein language models.

## Quick Start

### 1. Analyze All Antibodies

To run maturation analysis on a collection of predefined antibodies using all available models:

```bash
python3 bin/analyze_all_antibodies.py
```

#### Options

- `--output-dir DIR`  
  Directory to save log files (default: `logs`).

- `--antibody NAME`  
  Analyze a specific antibody by name (default: all antibodies).

- `--top-mutations N`  
  Number of top mutations to report (default: 20).

- `--verbose`  
  Print verbose output.

- `--dry-run`  
  Show commands without executing them.

Example (analyze only `mab114` with verbose output):

```bash
python3 bin/analyze_all_antibodies.py --antibody mab114 --verbose
```

### 2. Suggest Mutations for Custom Antibody Sequences

To suggest beneficial mutations for your own antibody sequences:

```bash
python3 bin/antibody_maturation.py --heavy <HEAVY_SEQ> --light <LIGHT_SEQ> --all-models --top-mutations 10
```

If you omit `--heavy` or `--light`, you will be prompted to enter sequences interactively.

#### Options

- `--heavy SEQUENCE`  
  Heavy chain amino acid sequence.

- `--light SEQUENCE`  
  Light chain amino acid sequence.

- `--all-models`  
  Use all available models for suggestions.

- `--model-name NAME`  
  Use a specific model (default: `igbert`).

- `--top-mutations N`  
  Number of top mutations to suggest (default: 100).

- `--verbose`  
  Enable verbose output.

Example (single model):

```bash
python3 bin/antibody_maturation.py --heavy <HEAVY_SEQ> --light <LIGHT_SEQ> --model-name esm1b --top-mutations 5
```

### Output

- The scripts print a ranked list of suggested mutations, indicating the antibody region (e.g., CDR1, FR2), chain, position, and which models suggested each mutation.
- Log files are saved in the specified output directory when using `analyze_all_antibodies.py`.

## Antibody Database

The following antibodies are available for batch analysis:

- `mab114`
- `mab114_uca`
- `s309`
- `regn10987`
- `medi8852`
- `c143`

## Requirements

- Python 3.7+
- See `requirements.txt` for dependencies.

## License

See `LICENSE` for details.
